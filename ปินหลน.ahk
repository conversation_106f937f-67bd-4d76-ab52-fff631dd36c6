#NoEnv
#SingleInstance, Force
#Persistent
#InstallKeybdHook
#UseHook
#KeyHistory, 0
#HotKeyInterval 1
#MaxHotkeysPerInterval 127
CoordMode, Pixel, Screen, RGB
CoordMode, Mouse, Screen
PID := DllCall("GetCurrentProcessId")
Process, Priority, %PID%, High
 
; GUI Window
Gui, +AlwaysOnTop
Width := 310
Gui, +LastFound
WinSet, Transparent, 230
Gui, Color, FFFFFF  ; Light background color
Gui, Margin, 0, 0
 
; GUI Title
Gui, Font, s10 cD0D0D0 Bold
Gui, Add, Progress, % "x-1 y-1 w" (Width+2) " h31 Background000000 Disabled hwndHPROG"
Control, ExStyle, -0x20000, , ahk_id %HPROG% ; probably only needed on Win XP
Gui, Add, Text, % "x0 y0 w" Width " h30 Background000000 Center 0x200 gGuiMove vCaption", Sai's Aim Assist
 
; GUI Body
Gui, <PERSON>ont, s8
Gui, Add, CheckBox, % "x7 y+10 w" (Width-14) "r1 +0x4000 vEnableCheckbox", Enable
Gui, Add, CheckBox, % "x7 y+10 w" (Width-14) "r1 +0x4000 vEnablePredictionCheckbox", Enable Prediction
Gui, Add, Text, % "x7 y+10 w" (Width-14) "r1 +0x4000", Target
Gui, Add, Button, % "x7 y+10 w" (Width-14) "r1 +0x4000 gHeadshotsButton", Head
Gui, Add, Button, % "x7 y+10 w" (Width-14) "r1 +0x4000 gChestButton", Chest
Gui, Add, Text, % "x7 y+10 w" (Width-14) "r1 +0x4000", Press ALT to toggle.
Gui, Add, Text, % "x7 y+10 w" (Width-14) "r1 +0x4000 gClose", Close
Gui, Add, Text, % "x7 y+15 w" "h5 vP"
GuiControlGet, P, Pos
H := PY + PH
Gui, -Caption
WinSet, Region, 0-0 w%Width% h%H% r6-6
 
; Add Smoothing Control
Gui, Add, Text, % "x7 y+50 w" (Width-14) "h20", Smoothing
Gui, Add, Button, % "x7 y+70 w50 h30 gDecreaseSmoothing", -
Gui, Add, Button, % "x57 y+70 w50 h30 gIncreaseSmoothing", +
Gui, Add, Text, % "x107 y+70 w80 h30 vSmoothingValue", %smoothing%
 
; Show GUI
Gui, Show, % "w" Width " NA" " x" (A_ScreenWidth - Width) "x10 y550"
 
; Settings
EMCol := #7B44A3  ; Target color (example)
ColVn := 50  ; Tolerance for color matching
ZeroX := A_ScreenWidth / 2
ZeroY := A_ScreenHeight / 2.07
CFovX := 100  ; Adjusted for a larger FOV
CFovY := 100  ; Adjusted for a larger FOV
ScanL := ZeroX - CFovX
ScanT := ZeroY - CFovY
ScanR := ZeroX + CFovX
ScanB := ZeroY + CFovY
SearchArea := 50  ; Smaller area around the last known position
 
; Variables for prediction
prevX := 0
prevY := 0
lastTime := 0
smoothing := 0.3  ; Default smoothing value
predictionMultiplier := 2.5  ; Adjust this to control how far ahead you predict
 
Loop
{
    ; Check if the script is enabled
    GuiControlGet, EnableState,, EnableCheckbox
    if (EnableState) {
        targetFound := False
        
        if GetKeyState("LButton", "P") or GetKeyState("RButton", "P") {
            ; Search for target pixel in a smaller region around the last known position
            PixelSearch, AimPixelX, AimPixelY, targetX - SearchArea, targetY - SearchArea, targetX + SearchArea, targetY + SearchArea, EMCol, ColVn, Fast RGB
            if (!ErrorLevel) {
                targetX := AimPixelX
                targetY := AimPixelY
                targetFound := True
            } else {
                PixelSearch, AimPixelX, AimPixelY, ScanL, ScanT, ScanR, ScanB, EMCol, ColVn, Fast RGB
                if (!ErrorLevel) {
                    targetX := AimPixelX
                    targetY := AimPixelY
                    targetFound := True
                }
            }
            
            if (targetFound) {
                ; Get current time
                currentTime := A_TickCount
 
                ; Calculate the velocity of the target
                if (lastTime != 0) {
                    deltaTime := (currentTime - lastTime) / 1000.0  ; Convert to seconds
                    velocityX := (targetX - prevX) / deltaTime
                    velocityY := (targetY - prevY) / deltaTime
                }
 
                ; Store the current position and time for the next iteration
                prevX := targetX
                prevY := targetY
                lastTime := currentTime
                
                ; Apply prediction if enabled
                GuiControlGet, PredictionEnabled,, EnablePredictionCheckbox
                if (PredictionEnabled && deltaTime != 0) {
                    PredictedX := targetX + Round(velocityX * predictionMultiplier * deltaTime)
                    PredictedY := targetY + Round(velocityY * predictionMultiplier * deltaTime)
                } else {
                    PredictedX := targetX
                    PredictedY := targetY
                }
 
                ; Move the mouse smoothly with strength adjustment
                AimX := PredictedX - ZeroX
                AimY := PredictedY - ZeroY
                DllCall("mouse_event", uint, 1, int, Round(AimX * smoothing), int, Round(AimY * smoothing), uint, 0, int, 0)
            }
        }
    }
    Sleep, 10
}
 
GuiControl,, ZeroYLabel, %ZeroY%
GuiControl,, CFovXLabel, %CFovX%
GuiControl,, CFovYLabel, %CFovY%
GuiControl,, SmoothingValue, %smoothing%
 
; Button callbacks for GUI
HeadshotsButton:
    ZeroY := A_ScreenHeight / 2.07
    GuiControl,, ZeroYLabel, %ZeroY%
    Return
 
ChestButton:
    ZeroY := A_ScreenHeight / 2.12
    GuiControl,, ZeroYLabel, %ZeroY%
    Return
 
GuiMove:
    PostMessage, 0xA1, 2
    return
 
IncreaseSmoothing:
    smoothing += 0.1
    if (smoothing > 2)  ; Set a maximum limit for smoothing
        smoothing := 2
    GuiControl,, SmoothingValue, %smoothing%
    Return
 
DecreaseSmoothing:
    smoothing -= 0.1
    if (smoothing < 0.1)  ; Set a minimum limit for smoothing
        smoothing := 0.1
    GuiControl,, SmoothingValue, %smoothing%
    Return
 
toggle := false
 
if (targetFound && toggle) {
    click down
} else {
    click up
}
 
Paused := False
Alt::
    ; Toggle the Enable checkbox state
    GuiControlGet, EnableState,, EnableCheckbox
    GuiControl,, EnableCheckbox, % !EnableState
    ; Toggle the script state based on the checkbox
    toggle := EnableState
    ; Play sound
    if (toggle) {
        SoundBeep, 300, 100
    }
Return
 
OnExit:
GuiClose:
    ExitApp
    Return    
 
Close:
ExitApp
 
f9::Reload